// This is a Node.js backend example using Express and Twilio
const express = require('express');
const bodyParser = require('body-parser');
const twilio = require('twilio');

const app = express();
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());
app.use(express.static('public'));

// Your Twilio credentials (you'll need to sign up at twilio.com)
const accountSid = 'YOUR_TWILIO_ACCOUNT_SID';
const authToken = 'YOUR_TWILIO_AUTH_TOKEN';
const twilioNumber = 'YOUR_TWILIO_PHONE_NUMBER';

const client = twilio(accountSid, authToken);

app.post('/api/make-call', (req, res) => {
    const phoneNumber = req.body.phoneNumber;
    
    // Format for international calling to Iran (+98)
    const formattedNumber = formatIranianNumber(phoneNumber);
    
    client.calls.create({
        url: 'http://demo.twilio.com/docs/voice.xml', // TwiML instructions for the call
        to: formattedNumber,
        from: twilioNumber
    })
    .then(call => {
        console.log('Call initiated with SID:', call.sid);
        res.json({ success: true, callSid: call.sid });
    })
    .catch(err => {
        console.error('Error making call:', err);
        res.status(500).json({ success: false, error: err.message });
    });
});

function formatIranianNumber(number) {
    // Convert 021XXXXXXXX to +9821XXXXXXXX format
    if (number.startsWith('0')) {
        return '+98' + number.substring(1);
    }
    return number;
}

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});