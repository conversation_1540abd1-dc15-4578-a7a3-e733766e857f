$(document).ready(function() {
    $("#callButton").click(function() {
        const phoneNumber = $("#phoneNumber").val().trim();
        
        // Validate Iranian number format
        if (!validateIranianNumber(phoneNumber)) {
            $("#callStatus").html("<p class='error'>Please enter a valid Iranian phone number</p>");
            return;
        }
        
        // Format the phone number
        const formattedNumber = formatIranianNumber(phoneNumber);
        
        // In a real application, you would make an AJAX call to your backend
        makeCall(formattedNumber);
    });
    
    function validateIranianNumber(number) {
        // Remove spaces, dashes, and other non-digit characters
        const cleaned = number.replace(/\D/g, '');
        
        // Check if it's a valid Iranian number format
        // Either starting with +98 (international format)
        // Or starting with 0 (local format)
        const iranIntlRegex = /^(98|0098)[0-9]{10}$/;
        const iranLocalRegex = /^0[0-9]{10}$/;
        
        return iranIntlRegex.test(cleaned) || iranLocalRegex.test(cleaned);
    }
    
    function formatIranianNumber(number) {
        // Remove spaces, dashes, and other non-digit characters
        const cleaned = number.replace(/\D/g, '');
        
        // If it starts with 98 or 0098, convert to +98 format
        if (cleaned.startsWith('98')) {
            return '+' + cleaned;
        } else if (cleaned.startsWith('0098')) {
            return '+' + cleaned.substring(2);
        } else if (cleaned.startsWith('0')) {
            // Convert local format (0XXX) to international (+98XXX)
            return '+98' + cleaned.substring(1);
        }
        
        // If it doesn't match any known format, add +98 prefix
        return '+98' + cleaned;
    }
    
    function makeCall(phoneNumber) {
        // This would normally call a backend API that uses Twilio
        $("#callStatus").html("<p>Initiating call to " + phoneNumber + "...</p>");
        
        // Simulating API call
        $.ajax({
            url: "/api/make-call", // This would be your backend endpoint
            method: "POST",
            data: { phoneNumber: phoneNumber },
            success: function(response) {
                $("#callStatus").html("<p class='success'>Call connected!</p>");
            },
            error: function(error) {
                $("#callStatus").html("<p class='error'>Failed to connect call. Please try again.</p>");
                console.error("Call failed:", error);
            }
        });
    }
});
